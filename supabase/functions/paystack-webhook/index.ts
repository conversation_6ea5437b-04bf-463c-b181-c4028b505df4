
import { serve } from "https://deno.land/std@0.168.0/http/server.ts";
import { createClient } from 'https://esm.sh/@supabase/supabase-js@2';

const corsHeaders = {
  'Access-Control-Allow-Origin': '*',
  'Access-Control-Allow-Headers': 'authorization, x-client-info, apikey, content-type, x-paystack-signature',
};

serve(async (req) => {
  if (req.method === 'OPTIONS') {
    return new Response('ok', { headers: corsHeaders });
  }

  try {
    const supabaseClient = createClient(
      Deno.env.get('SUPABASE_URL') ?? '',
      Deno.env.get('SUPABASE_SERVICE_ROLE_KEY') ?? ''
    );

    const signature = req.headers.get('x-paystack-signature');
    const body = await req.text();
    
    // Verify webhook signature (you should implement this with your webhook secret)
    // const hash = crypto.subtle.importKey('raw', new TextEncoder().encode(webhookSecret), { name: 'HMAC', hash: 'SHA-512' }, false, ['sign']);
    
    const event = JSON.parse(body);
    
    console.log('Paystack webhook event:', event.event);

    switch (event.event) {
      case 'subscription.create':
        await handleSubscriptionCreate(supabaseClient, event.data);
        break;
      case 'subscription.disable':
        await handleSubscriptionDisable(supabaseClient, event.data);
        break;
      case 'invoice.payment_failed':
        await handlePaymentFailed(supabaseClient, event.data);
        break;
      case 'charge.success':
        await handleChargeSuccess(supabaseClient, event.data);
        break;
      default:
        console.log('Unhandled event type:', event.event);
    }

    return new Response(JSON.stringify({ received: true }), {
      headers: { ...corsHeaders, 'Content-Type': 'application/json' },
      status: 200,
    });

  } catch (error) {
    console.error('Webhook error:', error);
    return new Response(JSON.stringify({ error: error.message }), {
      headers: { ...corsHeaders, 'Content-Type': 'application/json' },
      status: 400,
    });
  }
});

async function handleSubscriptionCreate(supabase: any, data: any) {
  const { error } = await supabase
    .from('user_subscriptions')
    .update({
      paystack_subscription_code: data.subscription_code,
      paystack_customer_code: data.customer.customer_code,
      status: 'active',
      current_period_start: new Date().toISOString(),
      current_period_end: new Date(data.next_payment_date).toISOString(),
    })
    .eq('user_id', data.metadata?.user_id);

  if (error) {
    console.error('Error updating subscription:', error);
  }
}

async function handleSubscriptionDisable(supabase: any, data: any) {
  const { error } = await supabase
    .from('user_subscriptions')
    .update({
      status: 'cancelled',
      cancelled_at: new Date().toISOString(),
    })
    .eq('paystack_subscription_code', data.subscription_code);

  if (error) {
    console.error('Error disabling subscription:', error);
  }
}

async function handlePaymentFailed(supabase: any, data: any) {
  const { error } = await supabase
    .from('user_subscriptions')
    .update({
      status: 'past_due',
    })
    .eq('paystack_subscription_code', data.subscription.subscription_code);

  if (error) {
    console.error('Error updating failed payment:', error);
  }
}

async function handleChargeSuccess(supabase: any, data: any) {
  // Record the transaction
  const { error: transactionError } = await supabase
    .from('payment_transactions')
    .insert({
      user_id: data.metadata?.user_id,
      paystack_reference: data.reference,
      paystack_transaction_id: data.id,
      amount_cents: data.amount,
      currency: data.currency,
      status: 'success',
      transaction_type: 'subscription',
      metadata: data.metadata,
    });

  if (transactionError) {
    console.error('Error recording transaction:', transactionError);
  }

  // If this is a daily subscription payment, extend the user's subscription by 1 day
  if (data.metadata?.subscription_type === 'daily' && data.metadata?.user_id) {
    const { error: extensionError } = await supabase
      .rpc('extend_user_subscription', {
        p_user_id: data.metadata.user_id,
        p_days: 1
      });

    if (extensionError) {
      console.error('Error extending subscription:', extensionError);
    } else {
      console.log('Successfully extended subscription for user:', data.metadata.user_id);
    }
  }
}
