-- Function to handle updated_at timestamps
CREATE OR <PERSON><PERSON><PERSON>CE FUNCTION public.handle_updated_at()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = NOW();
    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- Function to create profile on user signup and start free trial
CREATE OR REPLACE FUNCTION public.handle_new_user()
RETURNS TRIGGER AS $$
DECLARE
    default_plan_id UUID;
BEGIN
    -- Create user profile
    INSERT INTO public.profiles (id, email, full_name, country, course, institute)
    VALUES (
        NEW.id,
        NEW.email,
        NEW.raw_user_meta_data->>'full_name',
        NEW.raw_user_meta_data->>'country',
        NEW.raw_user_meta_data->>'course',
        NEW.raw_user_meta_data->>'institute'
    );

    -- Get the default subscription plan (daily plan)
    SELECT id INTO default_plan_id
    FROM public.subscription_plans
    WHERE interval_type = 'daily' AND is_active = true
    LIMIT 1;

    -- Start 1-minute free trial if plan exists
    IF default_plan_id IS NOT NULL THEN
        INSERT INTO public.user_subscriptions (
            user_id,
            plan_id,
            status,
            trial_ends_at,
            created_at,
            updated_at
        ) VALUES (
            NEW.id,
            default_plan_id,
            'active',
            NOW() + INTERVAL '1 minute',
            NOW(),
            NOW()
        );
    END IF;

    RETURN NEW;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Function to update user online status
CREATE OR REPLACE FUNCTION public.update_user_online_status(user_id UUID, is_online BOOLEAN)
RETURNS VOID AS $$
BEGIN
    UPDATE public.profiles 
    SET 
        is_online = update_user_online_status.is_online,
        last_seen = CASE WHEN update_user_online_status.is_online = false THEN NOW() ELSE last_seen END
    WHERE id = user_id;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Function to get user friends
CREATE OR REPLACE FUNCTION public.get_user_friends(p_user_id UUID)
RETURNS TABLE (
    id UUID,
    full_name TEXT,
    avatar_url TEXT,
    is_online BOOLEAN,
    last_seen TIMESTAMP WITH TIME ZONE
) AS $$
BEGIN
    RETURN QUERY
    SELECT 
        p.id,
        p.full_name,
        p.avatar_url,
        p.is_online,
        p.last_seen
    FROM public.profiles p
    INNER JOIN public.friendships f ON (
        (f.requester_id = p_user_id AND f.addressee_id = p.id) OR
        (f.addressee_id = p_user_id AND f.requester_id = p.id)
    )
    WHERE f.status = 'accepted';
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Function to get or create direct conversation
CREATE OR REPLACE FUNCTION public.get_or_create_conversation(user1_id UUID, user2_id UUID)
RETURNS UUID AS $$
DECLARE
    conversation_id UUID;
BEGIN
    -- Try to find existing conversation
    SELECT c.id INTO conversation_id
    FROM public.conversations c
    WHERE c.type = 'direct'
    AND EXISTS (
        SELECT 1 FROM public.conversation_participants cp1 
        WHERE cp1.conversation_id = c.id AND cp1.user_id = user1_id
    )
    AND EXISTS (
        SELECT 1 FROM public.conversation_participants cp2 
        WHERE cp2.conversation_id = c.id AND cp2.user_id = user2_id
    );
    
    -- If no conversation exists, create one
    IF conversation_id IS NULL THEN
        INSERT INTO public.conversations (type, created_by)
        VALUES ('direct', user1_id)
        RETURNING id INTO conversation_id;
        
        -- Add both users as participants
        INSERT INTO public.conversation_participants (conversation_id, user_id)
        VALUES 
            (conversation_id, user1_id),
            (conversation_id, user2_id);
    END IF;
    
    RETURN conversation_id;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Function to get user conversations with latest message
CREATE OR REPLACE FUNCTION public.get_user_conversations(p_user_id UUID)
RETURNS TABLE (
    conversation_id UUID,
    conversation_type TEXT,
    conversation_name TEXT,
    other_user_name TEXT,
    other_user_avatar TEXT,
    latest_message TEXT,
    latest_message_time TIMESTAMP WITH TIME ZONE,
    unread_count BIGINT
) AS $$
BEGIN
    RETURN QUERY
    SELECT 
        c.id as conversation_id,
        c.type as conversation_type,
        CASE 
            WHEN c.type = 'group' THEN c.name
            ELSE (
                SELECT p.full_name 
                FROM public.profiles p
                INNER JOIN public.conversation_participants cp ON cp.user_id = p.id
                WHERE cp.conversation_id = c.id AND cp.user_id != p_user_id
                LIMIT 1
            )
        END as conversation_name,
        CASE 
            WHEN c.type = 'direct' THEN (
                SELECT p.full_name 
                FROM public.profiles p
                INNER JOIN public.conversation_participants cp ON cp.user_id = p.id
                WHERE cp.conversation_id = c.id AND cp.user_id != p_user_id
                LIMIT 1
            )
            ELSE NULL
        END as other_user_name,
        CASE 
            WHEN c.type = 'direct' THEN (
                SELECT p.avatar_url 
                FROM public.profiles p
                INNER JOIN public.conversation_participants cp ON cp.user_id = p.id
                WHERE cp.conversation_id = c.id AND cp.user_id != p_user_id
                LIMIT 1
            )
            ELSE c.avatar_url
        END as other_user_avatar,
        (
            SELECT m.content 
            FROM public.messages m 
            WHERE m.conversation_id = c.id 
            ORDER BY m.created_at DESC 
            LIMIT 1
        ) as latest_message,
        (
            SELECT m.created_at 
            FROM public.messages m 
            WHERE m.conversation_id = c.id 
            ORDER BY m.created_at DESC 
            LIMIT 1
        ) as latest_message_time,
        0::BIGINT as unread_count -- TODO: Implement read receipts
    FROM public.conversations c
    INNER JOIN public.conversation_participants cp ON cp.conversation_id = c.id
    WHERE cp.user_id = p_user_id
    ORDER BY latest_message_time DESC NULLS LAST;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Triggers for updated_at
CREATE TRIGGER handle_updated_at_profiles
    BEFORE UPDATE ON public.profiles
    FOR EACH ROW EXECUTE FUNCTION public.handle_updated_at();

CREATE TRIGGER handle_updated_at_friendships
    BEFORE UPDATE ON public.friendships
    FOR EACH ROW EXECUTE FUNCTION public.handle_updated_at();

CREATE TRIGGER handle_updated_at_conversations
    BEFORE UPDATE ON public.conversations
    FOR EACH ROW EXECUTE FUNCTION public.handle_updated_at();

CREATE TRIGGER handle_updated_at_messages
    BEFORE UPDATE ON public.messages
    FOR EACH ROW EXECUTE FUNCTION public.handle_updated_at();

CREATE TRIGGER handle_updated_at_study_groups
    BEFORE UPDATE ON public.study_groups
    FOR EACH ROW EXECUTE FUNCTION public.handle_updated_at();

CREATE TRIGGER handle_updated_at_notes
    BEFORE UPDATE ON public.notes
    FOR EACH ROW EXECUTE FUNCTION public.handle_updated_at();

CREATE TRIGGER handle_updated_at_revision_plans
    BEFORE UPDATE ON public.revision_plans
    FOR EACH ROW EXECUTE FUNCTION public.handle_updated_at();

CREATE TRIGGER handle_updated_at_revision_tasks
    BEFORE UPDATE ON public.revision_tasks
    FOR EACH ROW EXECUTE FUNCTION public.handle_updated_at();

-- Trigger for new user signup
CREATE TRIGGER on_auth_user_created
    AFTER INSERT ON auth.users
    FOR EACH ROW EXECUTE FUNCTION public.handle_new_user();
