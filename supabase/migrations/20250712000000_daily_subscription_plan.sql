-- Migration to add daily subscription plan and update payment wall system
-- Created: 2025-07-12

-- First, update subscription_plans table to support daily intervals
ALTER TABLE public.subscription_plans 
ADD COLUMN IF NOT EXISTS interval_count INTEGER DEFAULT 1;

-- Add comment to clarify interval_count usage
COMMENT ON COLUMN public.subscription_plans.interval_count IS 'Number of intervals (e.g., 1 day, 7 days, 1 month)';

-- Insert daily subscription plan
INSERT INTO public.subscription_plans (
    name, 
    description, 
    price_cents, 
    currency, 
    interval_type, 
    interval_count,
    trial_period_days, 
    is_active
) VALUES (
    'StudyFam Daily', 
    'Daily access to all StudyFam premium features', 
    10, -- $0.10 per day
    'USD', 
    'daily', 
    1,
    0, -- No trial period for daily plan (trial handled separately)
    true
) ON CONFLICT DO NOTHING;

-- Update existing monthly plans to be inactive (we'll focus on daily)
UPDATE public.subscription_plans 
SET is_active = false 
WHERE interval_type = 'monthly';

-- Function to check if user subscription has expired (for payment wall)
CREATE OR REPLACE FUNCTION public.user_subscription_expired(p_user_id UUID)
RETURNS BOOLEAN AS $$
DECLARE
    subscription_record RECORD;
BEGIN
    -- Get the most recent active subscription
    SELECT 
        status,
        trial_ends_at,
        current_period_end
    INTO subscription_record
    FROM public.user_subscriptions
    WHERE user_id = p_user_id
    AND status IN ('active', 'past_due')
    ORDER BY created_at DESC
    LIMIT 1;
    
    -- If no subscription found, user needs to subscribe
    IF subscription_record IS NULL THEN
        RETURN true;
    END IF;
    
    -- Check if trial has expired
    IF subscription_record.trial_ends_at IS NOT NULL THEN
        IF subscription_record.trial_ends_at <= NOW() THEN
            -- Trial expired, check if there's a paid period
            IF subscription_record.current_period_end IS NULL OR subscription_record.current_period_end <= NOW() THEN
                RETURN true;
            END IF;
        END IF;
    -- Check if paid subscription has expired
    ELSIF subscription_record.current_period_end IS NOT NULL AND subscription_record.current_period_end <= NOW() THEN
        RETURN true;
    END IF;
    
    RETURN false;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Function to get user subscription status with expiration info
CREATE OR REPLACE FUNCTION public.get_user_subscription_status_v2(p_user_id UUID)
RETURNS TABLE (
    subscription_id UUID,
    plan_name VARCHAR,
    status VARCHAR,
    is_trial BOOLEAN,
    trial_ends_at TIMESTAMP WITH TIME ZONE,
    current_period_end TIMESTAMP WITH TIME ZONE,
    days_remaining INTEGER,
    minutes_remaining INTEGER,
    is_expired BOOLEAN,
    needs_payment BOOLEAN
) AS $$
BEGIN
    RETURN QUERY
    SELECT 
        us.id as subscription_id,
        sp.name as plan_name,
        us.status,
        (us.trial_ends_at IS NOT NULL AND us.trial_ends_at > NOW()) as is_trial,
        us.trial_ends_at,
        us.current_period_end,
        CASE 
            WHEN us.trial_ends_at IS NOT NULL AND us.trial_ends_at > NOW() THEN
                EXTRACT(DAY FROM us.trial_ends_at - NOW())::INTEGER
            WHEN us.current_period_end IS NOT NULL AND us.current_period_end > NOW() THEN
                EXTRACT(DAY FROM us.current_period_end - NOW())::INTEGER
            ELSE 0
        END as days_remaining,
        CASE 
            WHEN us.trial_ends_at IS NOT NULL AND us.trial_ends_at > NOW() THEN
                EXTRACT(EPOCH FROM us.trial_ends_at - NOW())::INTEGER / 60
            WHEN us.current_period_end IS NOT NULL AND us.current_period_end > NOW() THEN
                EXTRACT(EPOCH FROM us.current_period_end - NOW())::INTEGER / 60
            ELSE 0
        END as minutes_remaining,
        CASE 
            WHEN us.trial_ends_at IS NOT NULL AND us.trial_ends_at <= NOW() THEN
                (us.current_period_end IS NULL OR us.current_period_end <= NOW())
            WHEN us.current_period_end IS NOT NULL AND us.current_period_end <= NOW() THEN
                true
            ELSE false
        END as is_expired,
        public.user_subscription_expired(p_user_id) as needs_payment
    FROM public.user_subscriptions us
    JOIN public.subscription_plans sp ON us.plan_id = sp.id
    WHERE us.user_id = p_user_id
    AND us.status IN ('active', 'past_due')
    ORDER BY us.created_at DESC
    LIMIT 1;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Function to extend user subscription by 1 day after payment
CREATE OR REPLACE FUNCTION public.extend_user_subscription(
    p_user_id UUID,
    p_days INTEGER DEFAULT 1
)
RETURNS BOOLEAN AS $$
DECLARE
    subscription_record RECORD;
    new_end_date TIMESTAMP WITH TIME ZONE;
BEGIN
    -- Get the user's current subscription
    SELECT id, current_period_end, trial_ends_at
    INTO subscription_record
    FROM public.user_subscriptions
    WHERE user_id = p_user_id
    AND status = 'active'
    ORDER BY created_at DESC
    LIMIT 1;
    
    IF subscription_record IS NULL THEN
        RETURN false;
    END IF;
    
    -- Calculate new end date
    IF subscription_record.current_period_end IS NOT NULL AND subscription_record.current_period_end > NOW() THEN
        -- Extend from current end date
        new_end_date := subscription_record.current_period_end + (p_days || ' days')::INTERVAL;
    ELSE
        -- Start from now
        new_end_date := NOW() + (p_days || ' days')::INTERVAL;
    END IF;
    
    -- Update subscription
    UPDATE public.user_subscriptions
    SET 
        current_period_end = new_end_date,
        current_period_start = CASE 
            WHEN current_period_start IS NULL THEN NOW()
            ELSE current_period_start
        END,
        updated_at = NOW()
    WHERE id = subscription_record.id;
    
    RETURN true;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Grant necessary permissions
GRANT EXECUTE ON FUNCTION public.user_subscription_expired(UUID) TO authenticated;
GRANT EXECUTE ON FUNCTION public.get_user_subscription_status_v2(UUID) TO authenticated;
GRANT EXECUTE ON FUNCTION public.extend_user_subscription(UUID, INTEGER) TO authenticated;
