
import React from 'react';
import { SubscriptionCard } from '@/components/subscription/SubscriptionCard';
import { PaystackPayment } from '@/components/subscription/PaystackPayment';
import { useSubscription } from '@/hooks/useSubscription';
import PageHeader from '@/components/PageHeader';

const Subscription = () => {
  const { subscriptionStatus, hasActiveSubscription, refetchStatus } = useSubscription();

  return (
    <div className="min-h-screen bg-gray-50 p-4">
      <div className="max-w-4xl mx-auto space-y-6">
        <PageHeader 
          title="Subscription Management" 
          description="Manage your StudyFam premium subscription"
        />

        <div className="grid md:grid-cols-2 gap-6">
          <div>
            <SubscriptionCard />
          </div>

          {(!hasActiveSubscription || subscriptionStatus?.is_trial) && (
            <div>
              <PaystackPayment
                planName="StudyFam Daily"
                amount={0.10}
                isDaily={true}
                onSuccess={refetchStatus}
              />
            </div>
          )}
        </div>

        {hasActiveSubscription && (
          <div className="bg-white rounded-lg p-6 shadow-sm">
            <h3 className="text-lg font-semibold mb-4">Subscription Details</h3>
            <div className="grid md:grid-cols-2 gap-4 text-sm">
              <div>
                <span className="text-gray-600">Plan:</span>
                <p className="font-medium">{subscriptionStatus?.plan_name}</p>
              </div>
              <div>
                <span className="text-gray-600">Status:</span>
                <p className="font-medium capitalize">{subscriptionStatus?.status}</p>
              </div>
              {subscriptionStatus?.is_trial && (
                <div>
                  <span className="text-gray-600">Trial Ends:</span>
                  <p className="font-medium">
                    {subscriptionStatus.trial_ends_at ? 
                      new Date(subscriptionStatus.trial_ends_at).toLocaleString() : 'N/A'}
                  </p>
                </div>
              )}
              {!subscriptionStatus?.is_trial && subscriptionStatus?.current_period_end && (
                <div>
                  <span className="text-gray-600">Next Billing:</span>
                  <p className="font-medium">
                    {new Date(subscriptionStatus.current_period_end).toLocaleDateString()}
                  </p>
                </div>
              )}
            </div>
          </div>
        )}
      </div>
    </div>
  );
};

export default Subscription;
