
import { useState, useEffect } from 'react';
import { useUser } from '@/hooks/useAuth';
import { getUserSubscriptionStatus, checkActiveSubscription, checkSubscriptionExpired, startFreeTrial } from '@/services/pesapalService';
import { supabase } from '@/integrations/supabase/client';
import { toast } from 'sonner';

export interface SubscriptionStatus {
  subscription_id: string | null;
  plan_name: string | null;
  status: string;
  is_trial: boolean;
  trial_ends_at: string | null;
  current_period_end: string | null;
  days_remaining: number;
  minutes_remaining: number;
  is_expired: boolean;
  needs_payment: boolean;
}

export const useSubscription = () => {
  const userQuery = useUser();
  const user = userQuery.data;
  const [subscriptionStatus, setSubscriptionStatus] = useState<SubscriptionStatus | null>(null);
  const [isLoading, setIsLoading] = useState(true);
  const [hasActiveSubscription, setHasActiveSubscription] = useState(false);
  const [needsPayment, setNeedsPayment] = useState(true); // Default to true to show payment wall

  const fetchSubscriptionStatus = async () => {
    if (!user?.id) {
      console.log('No user ID, skipping subscription fetch');
      setIsLoading(false);
      return;
    }

    console.log('🔄 Starting subscription status fetch for user:', user.id);
    setIsLoading(true);

    // Use a timeout to ensure we don't get stuck
    const timeoutPromise = new Promise((_, reject) => {
      setTimeout(() => reject(new Error('Subscription fetch timeout')), 5000);
    });

    try {
      // Wrap the entire operation in a timeout
      await Promise.race([
        (async () => {
          console.log('📊 Calling getUserSubscriptionStatus...');
          const status = await getUserSubscriptionStatus(user.id);
          console.log('✅ getUserSubscriptionStatus result:', status);

          console.log('🔍 Calling checkActiveSubscription...');
          const hasActive = await checkActiveSubscription(user.id);
          console.log('✅ checkActiveSubscription result:', hasActive);

          console.log('⏰ Calling checkSubscriptionExpired...');
          const expired = await checkSubscriptionExpired(user.id);
          console.log('✅ checkSubscriptionExpired result:', expired);

          console.log('📋 Final subscription status results:', { status, hasActive, expired });

          setSubscriptionStatus(status);
          setHasActiveSubscription(Boolean(hasActive));
          setNeedsPayment(Boolean(expired));

          console.log('✅ Subscription status fetch completed successfully');
        })(),
        timeoutPromise
      ]);
    } catch (error) {
      console.error('❌ Error fetching subscription status:', error);

      // Set safe defaults that show payment wall if we can't determine status
      setSubscriptionStatus(null);
      setHasActiveSubscription(false);
      setNeedsPayment(true); // Show payment wall if we can't determine status

      console.log('🔧 Set safe defaults due to error');
    } finally {
      console.log('🏁 Setting isLoading to false');
      setIsLoading(false);
    }
  };

  const startTrial = async () => {
    if (!user?.id) return;

    try {
      // Get the default daily plan
      const { data: plans } = await supabase
        .from('subscription_plans')
        .select('*')
        .eq('is_active', true)
        .eq('interval_type', 'daily')
        .limit(1)
        .single();

      if (!plans) {
        toast.error('No subscription plan available');
        return;
      }

      await startFreeTrial(user.id, plans.id);
      toast.success('Free trial started! You have 1 minute to explore premium features.');
      await fetchSubscriptionStatus();
    } catch (error) {
      console.error('Error starting trial:', error);
      toast.error('Failed to start free trial');
    }
  };

  useEffect(() => {
    if (user?.id) {
      console.log('🚀 useEffect triggered, fetching subscription status');
      fetchSubscriptionStatus();

      // Add a timeout to prevent infinite loading
      const timeout = setTimeout(() => {
        console.log('⏰ Subscription fetch timeout, setting loading to false and showing payment wall');
        setIsLoading(false);
        setNeedsPayment(true); // Show payment wall on timeout
      }, 8000); // 8 second timeout

      return () => clearTimeout(timeout);
    } else if (userQuery.isLoading) {
      console.log('👤 User query still loading...');
      // Keep loading while user query is loading
    } else {
      console.log('👤 No user, setting loading to false and showing payment wall');
      setIsLoading(false);
      setNeedsPayment(true);
    }
  }, [user?.id, userQuery.isLoading]);

  return {
    subscriptionStatus,
    hasActiveSubscription,
    needsPayment,
    isLoading,
    startTrial,
    refetchStatus: fetchSubscriptionStatus,
  };
};
