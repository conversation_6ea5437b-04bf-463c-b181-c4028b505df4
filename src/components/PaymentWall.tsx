import React, { useState, useEffect } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Clock, CreditCard, Lock, Timer, Zap } from 'lucide-react';
// import { useSubscription } from '@/hooks/useSubscription';
import { useUser } from '@/hooks/useAuth';
// import { paypalService } from '@/services/paypalService';
import { toast } from 'sonner';

interface PaymentWallProps {
  children: React.ReactNode;
  showTrialInfo?: boolean;
  // Allow passing subscription data to avoid double-fetching
  subscriptionData?: {
    subscriptionStatus: any;
    needsPayment: boolean;
    hasActiveSubscription: boolean;
    isLoading: boolean;
  };
}

export const PaymentWall: React.FC<PaymentWallProps> = ({
  children,
  showTrialInfo = false,
  subscriptionData
}) => {
  console.log('💰 PaymentWall: Component starting to render');
  console.log('💰 PaymentWall: Props received:', { showTrialInfo, hasSubscriptionData: !!subscriptionData });

  const userQuery = useUser();
  const user = userQuery.data;

  // SIMPLIFIED: Don't use complex subscription checking for now
  const subscriptionStatus = null;
  const needsPayment = true; // Always show payment wall
  const isLoading = false;

  const [isPaymentLoading, setIsPaymentLoading] = useState(false);
  const [timeRemaining, setTimeRemaining] = useState<string>('');

  console.log('💰 PaymentWall: Component rendered with data:', {
    needsPayment,
    subscriptionStatus,
    isLoading,
    showTrialInfo,
    usingPassedData: !!subscriptionData,
    user: !!user
  });

  // Update countdown timer
  useEffect(() => {
    if (!subscriptionStatus) return;

    const updateTimer = () => {
      const now = new Date().getTime();
      let endTime: number;

      if (subscriptionStatus.is_trial && subscriptionStatus.trial_ends_at) {
        endTime = new Date(subscriptionStatus.trial_ends_at).getTime();
      } else if (subscriptionStatus.current_period_end) {
        endTime = new Date(subscriptionStatus.current_period_end).getTime();
      } else {
        setTimeRemaining('Expired');
        return;
      }

      const difference = endTime - now;

      if (difference > 0) {
        const minutes = Math.floor((difference % (1000 * 60 * 60)) / (1000 * 60));
        const seconds = Math.floor((difference % (1000 * 60)) / 1000);
        setTimeRemaining(`${minutes}m ${seconds}s`);
      } else {
        setTimeRemaining('Expired');
        // Timer expired
      }
    };

    updateTimer();
    const interval = setInterval(updateTimer, 1000);

    return () => clearInterval(interval);
  }, [subscriptionStatus]);

  const handlePayment = async () => {
    console.log('💳 PaymentWall: handlePayment called', { user, userQuery });

    if (!user?.email) {
      console.log('❌ PaymentWall: No user email found', { user });
      toast.error('Please log in to make a payment');
      return;
    }

    console.log('✅ PaymentWall: User found, proceeding with payment', { email: user.email, id: user.id });

    setIsPaymentLoading(true);
    try {
      const reference = `studyfam_daily_${Date.now()}_${user.id}`;

      // For now, show a message that PayPal integration is being set up
      toast.success('PayPal integration is being set up. Please try again later.');

      // TODO: Implement actual PayPal integration
      console.log('PayPal payment would be initialized here for:', {
        email: user.email,
        amount: 0.10,
        currency: 'USD',
        reference
      });
    } catch (error) {
      console.error('Payment initialization error:', error);
      toast.error('Failed to initialize payment');
    } finally {
      setIsPaymentLoading(false);
    }
  };

  // Skip loading state - always show payment wall

  // SIMPLIFIED: Always show payment wall
  console.log('💰 PaymentWall: Showing payment wall (simplified)');
  return (
      <div className="min-h-screen flex items-center justify-center bg-gradient-to-br from-violet-50 to-purple-50 p-4">
        <Card className="w-full max-w-md shadow-2xl border-0">
          <CardHeader className="text-center pb-4">
            <div className="mx-auto w-16 h-16 bg-gradient-to-br from-violet-500 to-purple-600 rounded-full flex items-center justify-center mb-4">
              <Lock className="w-8 h-8 text-white" />
            </div>
            <CardTitle className="text-2xl font-bold text-gray-800">
              Subscription Required
            </CardTitle>
            <CardDescription className="text-gray-600">
              {subscriptionStatus?.is_trial 
                ? 'Your free trial has ended. Continue enjoying StudyFam with a daily subscription!'
                : 'Your subscription has expired. Renew to continue accessing premium features.'
              }
            </CardDescription>
          </CardHeader>
          
          <CardContent className="space-y-6">
            {/* Current Status */}
            <div className="bg-red-50 border border-red-200 rounded-lg p-4">
              <div className="flex items-center justify-between">
                <div className="flex items-center space-x-2">
                  <Timer className="w-5 h-5 text-red-500" />
                  <span className="font-medium text-red-700">
                    {subscriptionStatus?.is_trial ? 'Trial Expired' : 'Subscription Expired'}
                  </span>
                </div>
                <Badge variant="destructive">
                  {timeRemaining}
                </Badge>
              </div>
            </div>

            {/* Daily Plan Features */}
            <div className="bg-gradient-to-r from-violet-500 to-purple-600 text-white rounded-lg p-6">
              <div className="flex items-center justify-between mb-4">
                <div>
                  <h3 className="text-xl font-bold">StudyFam Daily</h3>
                  <p className="text-violet-100">Full access for 24 hours</p>
                </div>
                <div className="text-right">
                  <div className="text-3xl font-bold">$0.10</div>
                  <div className="text-sm text-violet-100">per day</div>
                </div>
              </div>
              
              <div className="space-y-2 text-sm">
                <div className="flex items-center space-x-2">
                  <Zap className="w-4 h-4" />
                  <span>AI-powered study tools</span>
                </div>
                <div className="flex items-center space-x-2">
                  <Zap className="w-4 h-4" />
                  <span>Unlimited notes & documents</span>
                </div>
                <div className="flex items-center space-x-2">
                  <Zap className="w-4 h-4" />
                  <span>Study groups & collaboration</span>
                </div>
                <div className="flex items-center space-x-2">
                  <Zap className="w-4 h-4" />
                  <span>Advanced quiz generation</span>
                </div>
              </div>
            </div>

            {/* Payment Button */}
            <Button 
              onClick={handlePayment} 
              disabled={isPaymentLoading}
              className="w-full bg-gradient-to-r from-violet-600 to-purple-600 hover:from-violet-700 hover:to-purple-700 text-white font-semibold py-3 text-lg"
              size="lg"
            >
              {isPaymentLoading ? (
                <>
                  <div className="w-4 h-4 mr-2 animate-spin rounded-full border-2 border-white border-t-transparent" />
                  Processing...
                </>
              ) : (
                <>
                  <CreditCard className="w-5 h-5 mr-2" />
                  Subscribe for $0.10/day
                </>
              )}
            </Button>

            <p className="text-xs text-gray-500 text-center">
              Secure payment with PayPal. Pay daily, cancel anytime.
            </p>
          </CardContent>
        </Card>
      </div>
    );
};

export default PaymentWall;
