import React, { useEffect } from 'react';
import { useNavigate, useLocation } from 'react-router-dom';
import { useUser } from '@/hooks/useAuth';

interface AuthRedirectProps {
  children: React.ReactNode;
  redirectTo?: string;
  allowedPaths?: string[];
}

/**
 * Component that redirects authenticated users away from public pages
 * and unauthenticated users to login page from protected pages
 */
const AuthRedirect: React.FC<AuthRedirectProps> = ({
  children,
  redirectTo = '/dashboard',
  allowedPaths = []
}) => {
  const navigate = useNavigate();
  const location = useLocation();
  const userQuery = useUser();
  const user = userQuery.data;

  useEffect(() => {
    const currentPath = location.pathname;

    // Check localStorage for persistent auth state
    const storedAuthState = localStorage.getItem('studyfam_auth_state');
    const hasStoredAuth = storedAuthState === 'authenticated';

    // Public paths that authenticated users should be redirected away from
    const publicPaths = [
      '/',
      '/login',
      '/register',
      '/contact-us',
      '/privacy-policy',
      '/about-us',
      ...allowedPaths
    ];

    // If user is authenticated (either from hook or localStorage) and on a public page, redirect to dashboard
    if ((user || hasStoredAuth) && publicPaths.includes(currentPath)) {
      console.log('Authenticated user on public page, redirecting to:', redirectTo);
      navigate(redirectTo, { replace: true });
      return;
    }

    // If user is not authenticated and on a protected page, redirect to login
    const protectedPaths = [
      '/dashboard',
      '/upload-files',
      '/sort-notes',
      '/document',
      '/revision-planner',
      '/ask-ai-tutor',
      '/quiz-generator',
      '/image-to-notes',
      '/study-groups',
      '/past-papers',
      '/take-notes',
      '/library',
      '/progress',
      '/search',
      '/profile',
      '/discover',
      '/messages',
      '/reading-timetable',
      '/notifications'
    ];

    const isProtectedPath = protectedPaths.some(path => currentPath.startsWith(path));

    if (!user && isProtectedPath) {
      console.log('Unauthenticated user on protected page, redirecting to login');
      navigate('/login', {
        replace: true,
        state: { from: currentPath } // Save the intended destination
      });
      return;
    }
  }, [user, location.pathname, navigate, redirectTo, allowedPaths]);

  // No loading state - just render children immediately

  return <>{children}</>;
};

export default AuthRedirect;
