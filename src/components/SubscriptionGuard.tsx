import React from 'react';
import { useUser } from '@/hooks/useAuth';
import { Navigate } from 'react-router-dom';
import { toast } from 'sonner';

interface SubscriptionGuardProps {
  children: React.ReactNode;
  requiresSubscription?: boolean;
  showTrialBanner?: boolean;
  fallbackPath?: string;
}

export const SubscriptionGuard: React.FC<SubscriptionGuardProps> = ({
  children,
  requiresSubscription = true,
  showTrialBanner = false,
  fallbackPath = '/login'
}) => {
  console.log('🛡️ SubscriptionGuard: Component called');

  const userQuery = useUser();
  const user = userQuery.data;

  console.log('🛡️ SubscriptionGuard: User authenticated:', !!user);
  console.log('🛡️ SubscriptionGuard: User data:', user);

  // Redirect to login if not authenticated
  if (!user) {
    console.log('SubscriptionGuard: Redirecting to login - no user');
    return <Navigate to={fallbackPath} replace />;
  }

  // SIMPLIFIED: Show inline payment wall to avoid import issues
  console.log('🚨 SubscriptionGuard: Showing payment wall (simplified approach)');

  const handlePayPalPayment = () => {
    console.log('💳 PayPal payment initiated for user:', user.email);
    toast.success('PayPal integration coming soon! Payment for $0.10/day');
  };

  return (
    <div className="min-h-screen flex items-center justify-center bg-gradient-to-br from-violet-50 to-purple-50 p-4">
      <div className="w-full max-w-md bg-white rounded-lg shadow-2xl p-6">
        <div className="text-center mb-6">
          <div className="mx-auto w-16 h-16 bg-gradient-to-br from-violet-500 to-purple-600 rounded-full flex items-center justify-center mb-4">
            <span className="text-white text-2xl">🔒</span>
          </div>
          <h2 className="text-2xl font-bold text-gray-800 mb-2">
            Subscription Required
          </h2>
          <p className="text-gray-600">
            Your subscription has expired. Subscribe for $0.10/day to continue.
          </p>
        </div>

        <div className="bg-gradient-to-r from-violet-500 to-purple-600 text-white rounded-lg p-6 mb-6">
          <div className="flex items-center justify-between mb-4">
            <div>
              <h3 className="text-xl font-bold">StudyFam Daily</h3>
              <p className="text-violet-100">Full access for 24 hours</p>
            </div>
            <div className="text-right">
              <div className="text-3xl font-bold">$0.10</div>
              <div className="text-sm text-violet-100">per day</div>
            </div>
          </div>

          <div className="flex items-center space-x-2 text-sm text-violet-100">
            <span>✓</span>
            <span>All study materials</span>
          </div>
          <div className="flex items-center space-x-2 text-sm text-violet-100">
            <span>✓</span>
            <span>AI tutoring</span>
          </div>
          <div className="flex items-center space-x-2 text-sm text-violet-100">
            <span>✓</span>
            <span>Study groups</span>
          </div>
        </div>

        <button
          onClick={handlePayPalPayment}
          className="w-full bg-gradient-to-r from-violet-600 to-purple-600 hover:from-violet-700 hover:to-purple-700 text-white font-semibold py-3 text-lg rounded-lg transition-all duration-200 flex items-center justify-center space-x-2"
        >
          <span>💳</span>
          <span>Subscribe with PayPal - $0.10/day</span>
        </button>

        <p className="text-xs text-gray-500 text-center mt-4">
          Secure payment with PayPal. Pay daily, cancel anytime.
        </p>
      </div>
    </div>
  );
};

export default SubscriptionGuard;
