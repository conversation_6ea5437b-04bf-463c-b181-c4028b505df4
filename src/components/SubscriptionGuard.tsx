import React from 'react';
import { useUser } from '@/hooks/useAuth';
import PaymentWall from '@/components/PaymentWall';
import { Navigate } from 'react-router-dom';

interface SubscriptionGuardProps {
  children: React.ReactNode;
  requiresSubscription?: boolean;
  showTrialBanner?: boolean;
  fallbackPath?: string;
}

export const SubscriptionGuard: React.FC<SubscriptionGuardProps> = ({
  children,
  requiresSubscription = true,
  showTrialBanner = false,
  fallbackPath = '/login'
}) => {
  console.log('🛡️ SubscriptionGuard: Component called');

  const userQuery = useUser();
  const user = userQuery.data;

  console.log('🛡️ SubscriptionGuard: User authenticated:', !!user);
  console.log('🛡️ SubscriptionGuard: User data:', user);

  // Redirect to login if not authenticated
  if (!user) {
    console.log('SubscriptionGuard: Redirecting to login - no user');
    return <Navigate to={fallbackPath} replace />;
  }

  // SIMPLIFIED: Always show payment wall for now (just change payment method to Pesapal)
  console.log('🚨 SubscriptionGuard: Showing payment wall (simplified approach)');
  return (
    <PaymentWall showTrialInfo={showTrialBanner}>
      {children}
    </PaymentWall>
  );
};

export default SubscriptionGuard;
