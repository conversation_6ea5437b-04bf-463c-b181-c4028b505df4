import React from 'react';
import { useUser } from '@/hooks/useAuth';
import { useSubscription } from '@/hooks/useSubscription';
import PaymentWall from '@/components/PaymentWall';
import { Navigate } from 'react-router-dom';

interface SubscriptionGuardProps {
  children: React.ReactNode;
  requiresSubscription?: boolean;
  showTrialBanner?: boolean;
  fallbackPath?: string;
}

export const SubscriptionGuard: React.FC<SubscriptionGuardProps> = ({
  children,
  requiresSubscription = true,
  showTrialBanner = false,
  fallbackPath = '/login'
}) => {
  const userQuery = useUser();
  const user = userQuery.data;

  console.log('🛡️ SubscriptionGuard: User authenticated:', !!user);

  // Redirect to login if not authenticated
  if (!user) {
    console.log('SubscriptionGuard: Redirecting to login - no user');
    return <Navigate to={fallbackPath} replace />;
  }

  // SIMPLIFIED: Always show payment wall for now
  // This avoids the loading issues with subscription checking
  console.log('🚨 SubscriptionGuard: Showing payment wall (simplified approach)');
  return (
    <PaymentWall showTrialInfo={showTrialBanner}>
      {children}
    </PaymentWall>
  );
};

export default SubscriptionGuard;
