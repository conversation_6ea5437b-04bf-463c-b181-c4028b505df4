import React from 'react';
import { useUser } from '@/hooks/useAuth';
import { Navigate } from 'react-router-dom';
import SimplePaymentWall from '@/components/SimplePaymentWall';

interface SubscriptionGuardProps {
  children: React.ReactNode;
  requiresSubscription?: boolean;
  showTrialBanner?: boolean;
  fallbackPath?: string;
}

export const SubscriptionGuard: React.FC<SubscriptionGuardProps> = ({
  children,
  requiresSubscription = true,
  showTrialBanner = false,
  fallbackPath = '/login'
}) => {
  const userQuery = useUser();
  const user = userQuery.data;

  // If no subscription required, just show content
  if (!requiresSubscription) {
    return <>{children}</>;
  }

  // Redirect to login if not authenticated
  if (!user) {
    return <Navigate to={fallbackPath} replace />;
  }

  // For now, always show payment wall (we'll add subscription checking later)
  return (
    <SimplePaymentWall showTrialInfo={showTrialBanner}>
      {children}
    </SimplePaymentWall>
  );
};

export default SubscriptionGuard;
