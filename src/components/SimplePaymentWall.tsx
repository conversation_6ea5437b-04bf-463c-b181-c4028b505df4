import React, { useState } from 'react';
import { useUser } from '@/hooks/useAuth';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { CreditCard, Lock, Timer } from 'lucide-react';
import { toast } from 'sonner';

interface SimplePaymentWallProps {
  children: React.ReactNode;
  showTrialInfo?: boolean;
}

export const SimplePaymentWall: React.FC<SimplePaymentWallProps> = ({ 
  children, 
  showTrialInfo = false 
}) => {
  const userQuery = useUser();
  const user = userQuery.data;
  const [isPaymentLoading, setIsPaymentLoading] = useState(false);

  const handlePayPalPayment = async () => {
    if (!user?.email) {
      toast.error('Please log in to make a payment');
      return;
    }

    setIsPaymentLoading(true);
    try {
      // For now, just show a success message
      // TODO: Integrate actual PayPal payment
      toast.success('PayPal payment integration coming soon!');
      console.log('PayPal payment would be initiated for:', {
        email: user.email,
        amount: 0.10,
        currency: 'USD'
      });
    } catch (error) {
      console.error('Payment error:', error);
      toast.error('Payment failed. Please try again.');
    } finally {
      setIsPaymentLoading(false);
    }
  };

  // For now, always show the payment wall
  // Later we can add subscription checking logic here
  return (
    <div className="min-h-screen flex items-center justify-center bg-gradient-to-br from-violet-50 to-purple-50 p-4">
      <Card className="w-full max-w-md shadow-2xl">
        <CardHeader className="text-center">
          <div className="mx-auto w-16 h-16 bg-gradient-to-br from-violet-500 to-purple-600 rounded-full flex items-center justify-center mb-4">
            <Lock className="w-8 h-8 text-white" />
          </div>
          <CardTitle className="text-2xl font-bold text-gray-800">
            Subscription Required
          </CardTitle>
          <CardDescription className="text-gray-600">
            Subscribe for $0.10/day to access all StudyFam features
          </CardDescription>
        </CardHeader>

        <CardContent className="space-y-6">
          {/* Pricing Card */}
          <div className="bg-gradient-to-r from-violet-500 to-purple-600 text-white rounded-lg p-6">
            <div className="flex items-center justify-between mb-4">
              <div>
                <h3 className="text-xl font-bold">StudyFam Daily</h3>
                <p className="text-violet-100">Full access for 24 hours</p>
              </div>
              <div className="text-right">
                <div className="text-3xl font-bold">$0.10</div>
                <div className="text-sm text-violet-100">per day</div>
              </div>
            </div>
            
            <div className="space-y-2 text-sm text-violet-100">
              <div className="flex items-center space-x-2">
                <span>✓</span>
                <span>All study materials</span>
              </div>
              <div className="flex items-center space-x-2">
                <span>✓</span>
                <span>AI tutoring</span>
              </div>
              <div className="flex items-center space-x-2">
                <span>✓</span>
                <span>Study groups</span>
              </div>
              <div className="flex items-center space-x-2">
                <span>✓</span>
                <span>Past papers</span>
              </div>
            </div>
          </div>

          {/* Payment Button */}
          <Button 
            onClick={handlePayPalPayment}
            disabled={isPaymentLoading}
            className="w-full bg-gradient-to-r from-violet-600 to-purple-600 hover:from-violet-700 hover:to-purple-700 text-white font-semibold py-3 text-lg h-auto"
          >
            <CreditCard className="w-5 h-5 mr-2" />
            {isPaymentLoading ? 'Processing...' : 'Subscribe with PayPal - $0.10/day'}
          </Button>

          {/* Trial Info */}
          {showTrialInfo && (
            <div className="bg-blue-50 border border-blue-200 rounded-lg p-4">
              <div className="flex items-center space-x-2 text-blue-800">
                <Timer className="w-4 h-4" />
                <span className="text-sm font-medium">
                  Free trial available - 1 minute to explore!
                </span>
              </div>
            </div>
          )}

          {/* Security Note */}
          <p className="text-xs text-gray-500 text-center">
            Secure payment with PayPal. Pay daily, cancel anytime.
          </p>
        </CardContent>
      </Card>
    </div>
  );
};

export default SimplePaymentWall;
