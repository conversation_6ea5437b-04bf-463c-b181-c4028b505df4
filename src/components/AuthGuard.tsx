import React from 'react';
import { Navigate } from 'react-router-dom';
import { useUser } from '@/hooks/useAuth';

interface AuthGuardProps {
  children: React.ReactNode;
}

const AuthGuard: React.FC<AuthGuardProps> = ({ children }) => {
  console.log('🛡️ AuthGuard: Component called');

  const userQuery = useUser();
  const user = userQuery.data;

  console.log('🛡️ AuthGuard: User authenticated:', !!user);

  // Simplified: Don't show loading state, just check if user exists
  if (!user) {
    console.log('AuthGuard: Redirecting to login - no user');
    return <Navigate to="/login" replace />;
  }

  console.log('🛡️ AuthGuard: User found, rendering children');
  return <>{children}</>;
};

export default AuthGuard;
