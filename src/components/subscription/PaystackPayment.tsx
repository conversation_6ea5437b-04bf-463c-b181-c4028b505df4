
import React, { useState } from 'react';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Loader2, CreditCard } from 'lucide-react';
import { useUser } from '@/hooks/useAuth';
import { paystackService } from '@/services/paystackService';
import { toast } from 'sonner';

interface PaystackPaymentProps {
  planName: string;
  amount: number; // in dollars
  onSuccess?: () => void;
  isDaily?: boolean;
}

export const PaystackPayment: React.FC<PaystackPaymentProps> = ({
  planName,
  amount,
  onSuccess,
  isDaily = false,
}) => {
  const user = useUser();
  const [isLoading, setIsLoading] = useState(false);

  const handlePayment = async () => {
    if (!user?.email) {
      toast.error('Please log in to make a payment');
      return;
    }

    setIsLoading(true);
    try {
      const reference = `studyfam_${isDaily ? 'daily' : 'monthly'}_${Date.now()}_${user.id}`;

      const response = isDaily
        ? await paystackService.initializeDailySubscription(
            user.email,
            reference,
            {
              user_id: user.id,
              plan_name: planName,
              subscription_type: 'daily',
            }
          )
        : await paystackService.initializeTransaction(
            user.email,
            amount,
            reference,
            {
              user_id: user.id,
              plan_name: planName,
            }
          );

      if (response.status && response.data?.authorization_url) {
        // Redirect to Paystack payment page
        window.location.href = response.data.authorization_url;
      } else {
        throw new Error('Failed to initialize payment');
      }
    } catch (error) {
      console.error('Payment initialization error:', error);
      toast.error('Failed to initialize payment');
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <Card>
      <CardHeader>
        <CardTitle className="flex items-center space-x-2">
          <CreditCard className="w-5 h-5" />
          <span>Subscribe to {planName}</span>
        </CardTitle>
        <CardDescription>
          Secure payment powered by Paystack
        </CardDescription>
      </CardHeader>
      <CardContent className="space-y-4">
        <div className="bg-blue-50 p-4 rounded-lg">
          <div className="flex justify-between items-center">
            <span className="font-medium">
              {isDaily ? 'Daily Subscription' : 'Monthly Subscription'}
            </span>
            <span className="text-2xl font-bold text-blue-600">${amount}</span>
          </div>
          <p className="text-sm text-gray-600 mt-1">
            {isDaily ? 'Recurring daily payment' : 'Recurring monthly payment'}
          </p>
        </div>

        <Button 
          onClick={handlePayment} 
          disabled={isLoading}
          className="w-full"
          size="lg"
        >
          {isLoading ? (
            <>
              <Loader2 className="w-4 h-4 mr-2 animate-spin" />
              Processing...
            </>
          ) : (
            <>
              <CreditCard className="w-4 h-4 mr-2" />
              Pay ${amount}/{isDaily ? 'day' : 'month'}
            </>
          )}
        </Button>

        <p className="text-xs text-gray-500 text-center">
          Secure payment with bank-level encryption. Cancel anytime.
        </p>
      </CardContent>
    </Card>
  );
};
