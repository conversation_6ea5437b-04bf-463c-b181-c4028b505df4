
import { supabase } from '@/integrations/supabase/client';

export interface PaystackCustomer {
  email: string;
  first_name?: string;
  last_name?: string;
  metadata?: Record<string, any>;
}

export interface PaystackSubscription {
  customer: string;
  plan: string;
  authorization: string;
  start_date?: string;
}

export interface PaystackPlan {
  name: string;
  interval: 'monthly' | 'yearly';
  amount: number; // in kobo/cents
  currency?: string;
}

class PaystackService {
  private readonly baseUrl = 'https://api.paystack.co';
  private readonly publicKey: string;
  private readonly secretKey: string;

  constructor() {
    // For now, we'll use the actual Paystack keys
    // You need to replace these with your actual Paystack keys
    this.publicKey = import.meta.env.VITE_PAYSTACK_PUBLIC_KEY || 'pk_test_your_public_key';
    this.secretKey = 'YOUR_ACTUAL_PAYSTACK_SECRET_KEY_HERE'; // Replace with your actual secret key
  }

  private async makeRequest(endpoint: string, options: RequestInit = {}) {
    const response = await fetch(`${this.baseUrl}${endpoint}`, {
      ...options,
      headers: {
        'Authorization': `Bearer ${this.secretKey}`,
        'Content-Type': 'application/json',
        ...options.headers,
      },
    });

    if (!response.ok) {
      const error = await response.json();
      throw new Error(error.message || 'Paystack API error');
    }

    return response.json();
  }

  async createPlan(planData: PaystackPlan) {
    return this.makeRequest('/plan', {
      method: 'POST',
      body: JSON.stringify(planData),
    });
  }

  async createCustomer(customerData: PaystackCustomer) {
    return this.makeRequest('/customer', {
      method: 'POST',
      body: JSON.stringify(customerData),
    });
  }

  async createSubscription(subscriptionData: PaystackSubscription) {
    return this.makeRequest('/subscription', {
      method: 'POST',
      body: JSON.stringify(subscriptionData),
    });
  }

  async initializeTransaction(email: string, amount: number, reference: string, metadata?: any) {
    return this.makeRequest('/transaction/initialize', {
      method: 'POST',
      body: JSON.stringify({
        email,
        amount: amount * 100, // Convert to kobo
        reference,
        metadata,
        callback_url: `${window.location.origin}/payment/callback`,
      }),
    });
  }

  async initializeDailySubscription(email: string, reference: string, metadata?: any) {
    // Daily subscription for $0.10
    return this.initializeTransaction(email, 0.10, reference, metadata);
  }

  async verifyTransaction(reference: string) {
    return this.makeRequest(`/transaction/verify/${reference}`);
  }

  async cancelSubscription(code: string, token: string) {
    return this.makeRequest('/subscription/disable', {
      method: 'POST',
      body: JSON.stringify({
        code,
        token,
      }),
    });
  }

  getPublicKey() {
    return this.publicKey;
  }
}

export const paystackService = new PaystackService();

// Subscription management functions for frontend
export async function startFreeTrial(userId: string, planId: string) {
  const trialEndDate = new Date();
  trialEndDate.setMinutes(trialEndDate.getMinutes() + 1); // 1 minute trial as requested

  const { data, error } = await supabase
    .from('user_subscriptions')
    .insert({
      user_id: userId,
      plan_id: planId,
      status: 'active',
      trial_ends_at: trialEndDate.toISOString(),
    })
    .select()
    .single();

  if (error) throw error;
  return data;
}

export async function getUserSubscriptionStatus(userId: string) {
  const { data, error } = await supabase
    .rpc('get_user_subscription_status_v2', { p_user_id: userId });

  if (error) throw error;
  return data?.[0] || null;
}

export async function checkActiveSubscription(userId: string) {
  const { data, error } = await supabase
    .rpc('user_has_active_subscription', { p_user_id: userId });

  if (error) throw error;
  return data;
}

export async function checkSubscriptionExpired(userId: string) {
  const { data, error } = await supabase
    .rpc('user_subscription_expired', { p_user_id: userId });

  if (error) throw error;
  return data;
}

export async function extendUserSubscription(userId: string, days: number = 1) {
  const { data, error } = await supabase
    .rpc('extend_user_subscription', { p_user_id: userId, p_days: days });

  if (error) throw error;
  return data;
}
