import { supabase } from '@/integrations/supabase/client';

export interface PesapalTransactionRequest {
  id: string;
  currency: string;
  amount: number;
  description: string;
  callback_url: string;
  notification_id: string;
  billing_address: {
    email_address: string;
    phone_number?: string;
    country_code?: string;
    first_name?: string;
    middle_name?: string;
    last_name?: string;
    line_1?: string;
    line_2?: string;
    city?: string;
    state?: string;
    postal_code?: string;
    zip_code?: string;
  };
}

export interface PesapalTransactionResponse {
  order_tracking_id: string;
  merchant_reference: string;
  redirect_url: string;
}

class PesapalService {
  private readonly baseUrl = 'https://cybqa.pesapal.com/pesapalv3/api'; // Sandbox URL
  private readonly clientId: string;
  private readonly clientSecret: string;
  private accessToken: string | null = null;
  private tokenExpiry: number = 0;

  constructor() {
    this.clientId = 'AYfVu3p-r1S2CzkcBXXIx3NsdwDdEZi8TB4RNSlVNTTL3yNH-c8eH5YMgf6OWfP8HXUwXFogcpWRZTwu';
    this.clientSecret = 'EBpZpCwo3j86iiNf6FBHwAMgPyW-0e7FAIKse2DB8tKoUS5qAZmPOFuYTK_vmFdtRNQqDIyREBsHOOeT';
  }

  private async getAccessToken(): Promise<string> {
    // Check if we have a valid token
    if (this.accessToken && Date.now() < this.tokenExpiry) {
      return this.accessToken;
    }

    console.log('🔑 Pesapal: Getting new access token');

    const response = await fetch(`${this.baseUrl}/Auth/RequestToken`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Accept': 'application/json',
      },
      body: JSON.stringify({
        consumer_key: this.clientId,
        consumer_secret: this.clientSecret,
      }),
    });

    if (!response.ok) {
      const errorData = await response.json().catch(() => ({}));
      console.error('Pesapal auth error:', errorData);
      throw new Error('Failed to authenticate with Pesapal');
    }

    const data = await response.json();
    this.accessToken = data.token;
    this.tokenExpiry = Date.now() + (data.expiryDate ? new Date(data.expiryDate).getTime() - Date.now() : 3600000); // 1 hour default

    console.log('✅ Pesapal: Access token obtained');
    return this.accessToken;
  }

  private async makeRequest(endpoint: string, options: RequestInit = {}) {
    const token = await this.getAccessToken();

    const response = await fetch(`${this.baseUrl}${endpoint}`, {
      ...options,
      headers: {
        'Authorization': `Bearer ${token}`,
        'Content-Type': 'application/json',
        'Accept': 'application/json',
        ...options.headers,
      },
    });

    if (!response.ok) {
      const errorData = await response.json().catch(() => ({}));
      console.error('Pesapal API error:', errorData);
      throw new Error(errorData.error_message || 'Pesapal API error');
    }

    return response.json();
  }

  async initializeTransaction(
    email: string,
    amount: number,
    reference: string,
    description: string = 'StudyFam Daily Subscription'
  ): Promise<PesapalTransactionResponse> {
    console.log('💳 Pesapal: Initializing transaction', { email, amount, reference });

    const transactionData: PesapalTransactionRequest = {
      id: reference,
      currency: 'USD',
      amount,
      description,
      callback_url: `${window.location.origin}/payment/callback?reference=${reference}`,
      notification_id: reference,
      billing_address: {
        email_address: email,
        country_code: 'KE', // Default to Kenya, can be made dynamic
        first_name: email.split('@')[0], // Extract name from email
      },
    };

    const response = await this.makeRequest('/Transactions/SubmitOrderRequest', {
      method: 'POST',
      body: JSON.stringify(transactionData),
    });

    console.log('✅ Pesapal: Transaction initialized', response);
    return response;
  }

  async verifyTransaction(orderTrackingId: string) {
    console.log('🔍 Pesapal: Verifying transaction', { orderTrackingId });

    const response = await this.makeRequest(`/Transactions/GetTransactionStatus?orderTrackingId=${orderTrackingId}`);

    console.log('✅ Pesapal: Transaction verified', response);
    return response;
  }

  async initializeDailySubscription(
    email: string,
    reference: string,
    metadata?: any
  ) {
    // Daily subscription for $0.10
    return this.initializeTransaction(
      email,
      0.10,
      reference,
      'StudyFam Daily Subscription - $0.10/day'
    );
  }
}

export const pesapalService = new PesapalService();

// Subscription management functions (keeping the same interface as Paystack)
export async function getUserSubscriptionStatus(userId: string) {
  const { data, error } = await supabase
    .rpc('get_user_subscription_status_v2', { p_user_id: userId });

  if (error) throw error;
  return data?.[0] || null;
}

export async function checkActiveSubscription(userId: string) {
  const { data, error } = await supabase
    .rpc('user_has_active_subscription', { p_user_id: userId });

  if (error) throw error;
  return data;
}

export async function checkSubscriptionExpired(userId: string) {
  const { data, error } = await supabase
    .rpc('user_subscription_expired', { p_user_id: userId });

  if (error) throw error;
  return data;
}

export async function extendUserSubscription(userId: string, days: number = 1) {
  const { data, error } = await supabase
    .rpc('extend_user_subscription', { p_user_id: userId, p_days: days });

  if (error) throw error;
  return data;
}

export async function startFreeTrial(userId: string, planId: string) {
  const trialEndDate = new Date();
  trialEndDate.setMinutes(trialEndDate.getMinutes() + 1); // 1 minute trial

  const { data, error } = await supabase
    .from('user_subscriptions')
    .insert({
      user_id: userId,
      plan_id: planId,
      status: 'active',
      trial_ends_at: trialEndDate.toISOString(),
    })
    .select()
    .single();

  if (error) throw error;
  return data;
}
