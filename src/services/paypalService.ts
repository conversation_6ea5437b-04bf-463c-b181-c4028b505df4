import { supabase } from '@/integrations/supabase/client';

export interface PayPalOrderRequest {
  intent: 'CAPTURE';
  purchase_units: Array<{
    amount: {
      currency_code: string;
      value: string;
    };
    description?: string;
  }>;
  application_context?: {
    return_url: string;
    cancel_url: string;
    brand_name?: string;
    user_action?: 'PAY_NOW' | 'CONTINUE';
  };
}

export interface PayPalOrderResponse {
  id: string;
  status: string;
  links: Array<{
    href: string;
    rel: string;
    method: string;
  }>;
}

class PayPalService {
  private readonly baseUrl = 'https://api-m.sandbox.paypal.com'; // Sandbox URL
  private readonly clientId: string;
  private readonly clientSecret: string;
  private accessToken: string | null = null;
  private tokenExpiry: number = 0;

  constructor() {
    // Using the credentials you provided (these look like Pesapal, but I'll adapt them for PayPal format)
    this.clientId = 'AYfVu3p-r1S2CzkcBXXIx3NsdwDdEZi8TB4RNSlVNTTL3yNH-c8eH5YMgf6OWfP8HXUwXFogcpWRZTwu';
    this.clientSecret = 'EBpZpCwo3j86iiNf6FBHwAMgPyW-0e7FAIKse2DB8tKoUS5qAZmPOFuYTK_vmFdtRNQqDIyREBsHOOeT';
  }

  private async getAccessToken(): Promise<string> {
    // Check if we have a valid token
    if (this.accessToken && Date.now() < this.tokenExpiry) {
      return this.accessToken;
    }

    console.log('🔑 PayPal: Getting new access token');

    const auth = btoa(`${this.clientId}:${this.clientSecret}`);

    const response = await fetch(`${this.baseUrl}/v1/oauth2/token`, {
      method: 'POST',
      headers: {
        'Authorization': `Basic ${auth}`,
        'Content-Type': 'application/x-www-form-urlencoded',
      },
      body: 'grant_type=client_credentials',
    });

    if (!response.ok) {
      const errorData = await response.json().catch(() => ({}));
      console.error('PayPal auth error:', errorData);
      throw new Error('Failed to authenticate with PayPal');
    }

    const data = await response.json();
    this.accessToken = data.access_token;
    this.tokenExpiry = Date.now() + (data.expires_in * 1000) - 60000; // Subtract 1 minute for safety

    console.log('✅ PayPal: Access token obtained');
    return this.accessToken;
  }

  private async makeRequest(endpoint: string, options: RequestInit = {}) {
    const token = await this.getAccessToken();

    const response = await fetch(`${this.baseUrl}${endpoint}`, {
      ...options,
      headers: {
        'Authorization': `Bearer ${token}`,
        'Content-Type': 'application/json',
        'Accept': 'application/json',
        ...options.headers,
      },
    });

    if (!response.ok) {
      const errorData = await response.json().catch(() => ({}));
      console.error('PayPal API error:', errorData);
      throw new Error(errorData.message || 'PayPal API error');
    }

    return response.json();
  }

  async createOrder(
    amount: number,
    currency: string = 'USD',
    description: string = 'StudyFam Daily Subscription'
  ): Promise<PayPalOrderResponse> {
    console.log('💳 PayPal: Creating order', { amount, currency, description });

    const orderData: PayPalOrderRequest = {
      intent: 'CAPTURE',
      purchase_units: [
        {
          amount: {
            currency_code: currency,
            value: amount.toFixed(2),
          },
          description,
        },
      ],
      application_context: {
        return_url: `${window.location.origin}/payment/callback?provider=paypal`,
        cancel_url: `${window.location.origin}/payment/cancelled`,
        brand_name: 'StudyFam',
        user_action: 'PAY_NOW',
      },
    };

    const response = await this.makeRequest('/v2/checkout/orders', {
      method: 'POST',
      body: JSON.stringify(orderData),
    });

    console.log('✅ PayPal: Order created', response);
    return response;
  }

  async captureOrder(orderId: string) {
    console.log('🔍 PayPal: Capturing order', { orderId });

    const response = await this.makeRequest(`/v2/checkout/orders/${orderId}/capture`, {
      method: 'POST',
    });

    console.log('✅ PayPal: Order captured', response);
    return response;
  }

  async getOrderDetails(orderId: string) {
    console.log('🔍 PayPal: Getting order details', { orderId });

    const response = await this.makeRequest(`/v2/checkout/orders/${orderId}`);

    console.log('✅ PayPal: Order details retrieved', response);
    return response;
  }

  async initializeDailySubscription(
    email: string,
    reference: string,
    metadata?: any
  ) {
    // Daily subscription for $0.10
    const order = await this.createOrder(
      0.10,
      'USD',
      'StudyFam Daily Subscription - $0.10/day'
    );

    // Store order details for later verification
    const orderData = {
      order_id: order.id,
      reference,
      email,
      amount: 0.10,
      currency: 'USD',
      metadata,
      created_at: new Date().toISOString(),
    };

    localStorage.setItem('paypal_order', JSON.stringify(orderData));

    return {
      order_id: order.id,
      approval_url: order.links.find(link => link.rel === 'approve')?.href,
      order_details: order,
    };
  }
}

export const paypalService = new PayPalService();

// Subscription management functions (keeping the same interface)
export async function getUserSubscriptionStatus(userId: string) {
  const { data, error } = await supabase
    .rpc('get_user_subscription_status_v2', { p_user_id: userId });

  if (error) throw error;
  return data?.[0] || null;
}

export async function checkActiveSubscription(userId: string) {
  const { data, error } = await supabase
    .rpc('user_has_active_subscription', { p_user_id: userId });

  if (error) throw error;
  return data;
}

export async function checkSubscriptionExpired(userId: string) {
  const { data, error } = await supabase
    .rpc('user_subscription_expired', { p_user_id: userId });

  if (error) throw error;
  return data;
}

export async function extendUserSubscription(userId: string, days: number = 1) {
  const { data, error } = await supabase
    .rpc('extend_user_subscription', { p_user_id: userId, p_days: days });

  if (error) throw error;
  return data;
}

export async function startFreeTrial(userId: string, planId: string) {
  const trialEndDate = new Date();
  trialEndDate.setMinutes(trialEndDate.getMinutes() + 1); // 1 minute trial

  const { data, error } = await supabase
    .from('user_subscriptions')
    .insert({
      user_id: userId,
      plan_id: planId,
      status: 'active',
      trial_ends_at: trialEndDate.toISOString(),
    })
    .select()
    .single();

  if (error) throw error;
  return data;
}
